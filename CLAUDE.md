# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a subscription converter service that runs on Cloudflare Workers. It converts proxy node configurations between different formats (V2Ray, Clash, Surge) and provides a web-based management interface for adding/managing proxy nodes and trusted email addresses.

## Development Commands

### Core Commands
- `npm run dev` - Start local development server using Wrangler
- `npm run deploy` - Deploy to Cloudflare Workers
- `npm run tail` - View Worker logs
- `npm run setup` - Run deployment setup script
- `npm run test` - Run tests (via test.js file)

### Deployment Commands
- `npx wrangler login` - Login to Cloudflare account
- `npx wrangler deploy` - Deploy Worker directly

## Architecture Overview

### Core Components

**Main Application (`src/index.js`)**
- Primary Worker entry point with request routing
- Handles authentication, node management, email management
- Serves HTML templates and API endpoints
- Routes: `/`, `/login`, `/logout`, `/add-node`, `/delete-node`, `/edit-node`, `/add-email`, `/delete-email`
- Subscription endpoints: `/v2ray`, `/clash`, `/surge` (require email parameter)

**Authentication System (`src/auth.js`)**
- Simple JWT-based authentication using Base64 encoding
- Cookie-based session management with HttpOnly, Secure flags
- Username/password validation against environment variables
- 24-hour token expiration

**Node Converter (`src/converter.js`)**
- Parses VMess and VLess proxy configurations
- Converts nodes to Clash YAML format with comprehensive rule sets
- Generates V2Ray Base64 subscription format
- Supports complex Clash configurations with proxy groups and rule providers

### Data Storage

**Cloudflare KV Storage**
- `nodes` - Array of parsed proxy node configurations
- `trusted_emails` - Array of authorized email addresses for subscription access

**Node Data Structure**
```javascript
{
  id: string,
  type: 'vmess' | 'vless',
  name: string,
  server: string,
  port: number,
  uuid: string,
  // Type-specific fields for network, security, etc.
}
```

### Security Model

**Authentication**
- Admin credentials stored in `ADMIN_USERNAME` and `ADMIN_PASSWORD` environment variables
- All management operations require authentication
- Subscription access requires email verification

**Email Verification**
- All subscription endpoints (`/v2ray`, `/clash`, `/surge`) require `email` parameter
- Only emails in the trusted list can access subscriptions
- Email management (add/delete) requires admin authentication

### Configuration Requirements

**Environment Variables (wrangler.toml)**
```toml
[vars]
ADMIN_USERNAME = "your-username"
ADMIN_PASSWORD = "your-password"

[[kv_namespaces]]
binding = "NODES_KV"
id = "your-kv-namespace-id"
```

**KV Namespace Setup**
- Create KV namespace in Cloudflare Dashboard
- Update `wrangler.toml` with the namespace ID
- Used for storing nodes and trusted emails

### Supported Proxy Types

**VMess**
- Full support for various transport protocols (TCP, WebSocket, HTTP/2, gRPC)
- TLS configuration with SNI support
- Header type configuration for TCP transport

**VLess**
- TCP, WebSocket, gRPC, HTTP/2 transport support
- TLS and Reality security protocols
- Flow control (XTLS) support
- Client fingerprint configuration

### Subscription Formats

**V2Ray Subscription**
- Base64 encoded list of VMess/VLess links
- Standard format compatible with V2Ray clients

**Clash Configuration**
- Complete YAML configuration with proxy groups
- Comprehensive rule sets for traffic routing
- DNS configuration with fake-ip support
- TUN mode support

**Surge Configuration**
- Limited to Shadowsocks and HTTP proxy types
- Basic proxy group configuration

## Key Implementation Details

### Template System
- HTML templates are embedded directly in `src/index.js`
- CSS styles are inline for self-contained deployment
- Responsive design optimized for desktop, tablet, and mobile

### Error Handling
- Comprehensive validation for node configurations
- Duplicate node detection based on server/port/uuid
- Form validation with user-friendly error messages

### Logging
- Login attempts are logged with IP, browser, and location info
- Uses Cloudflare request properties for geolocation data

## Testing and Development

When testing subscription conversions, ensure:
1. Node configurations are valid and complete
2. Email addresses are added to trusted list before testing subscriptions
3. Use browser access to view formatted subscription content
4. Client access returns raw subscription data

## Common Issues

- **KV Namespace Errors**: Verify namespace ID in wrangler.toml matches Cloudflare Dashboard
- **Authentication Failures**: Check ADMIN_USERNAME/ADMIN_PASSWORD in environment variables
- **Subscription Access Denied**: Ensure email is in trusted list via admin panel
- **Node Parsing Failures**: Validate node link format and required parameters