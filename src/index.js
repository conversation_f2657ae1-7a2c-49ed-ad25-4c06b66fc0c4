import { Auth } from './auth.js';
import { NodeConverter } from './converter.js';

// 模板文件内容 (在实际部署时需要读取文件)
const templates = {
  css: `/* CSS content will be loaded here */`,
  login: `/* Login HTML will be loaded here */`,
  dashboard: `/* Dashboard HTML will be loaded here */`
};

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const auth = new Auth(env);
    const converter = new NodeConverter();

    // 路由处理
    switch (url.pathname) {
      case '/':
        return this.handleHome(request, auth, env);
      
      case '/login':
        return this.handleLogin(request, auth, env);
      
      case '/logout':
        return this.handleLogout(request, auth);
      
      case '/add-node':
        return this.handleAddNode(request, auth, converter, env);
      
      case '/delete-node':
        return this.handleDeleteNode(request, auth, env);

      case '/edit-node':
        return this.handleEditNode(request, auth, converter, env);

      case '/add-email':
        return this.handleAddEmail(request, auth, env);

      case '/delete-email':
        return this.handleDeleteEmail(request, auth, env);

      case '/v2ray':
        return this.handleV2raySubscription(converter, env, request);

      case '/clash':
        return this.handleClashSubscription(converter, env, request);

      case '/surge':
        return this.handleSurgeSubscription(converter, env, request);

      default:
        return new Response('Not Found', { status: 404 });
    }
  },

  // 处理首页
  async handleHome(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return this.renderLogin();
    }
    return this.renderDashboard(request, env);
  },

  // 处理登录
  async handleLogin(request, auth, env) {
    if (request.method === 'GET') {
      return this.renderLogin();
    }

    if (request.method === 'POST') {
      const formData = await request.formData();
      const username = formData.get('username');
      const password = formData.get('password');

      // 获取用户基本信息
      const userInfo = this.getUserInfo(request);

      if (auth.validateCredentials(username, password)) {
        // 登录成功，记录信息
        console.log('✅ 登录成功:', {
          username: username,
          timestamp: new Date().toISOString(),
          ip: userInfo.ip,
          userAgent: userInfo.userAgent,
          browser: userInfo.browser,
          platform: userInfo.platform,
          country: userInfo.country,
          city: userInfo.city
        });

        const token = auth.generateToken(username);
        const response = new Response('', {
          status: 302,
          headers: {
            'Location': '/',
            'Set-Cookie': auth.createAuthCookie(token)
          }
        });
        return response;
      } else {
        // 登录失败，记录尝试信息
        console.log('❌ 登录失败:', {
          attemptedUsername: username,
          attemptedPassword: password,
          timestamp: new Date().toISOString(),
          ip: userInfo.ip,
          userAgent: userInfo.userAgent,
          browser: userInfo.browser,
          platform: userInfo.platform,
          country: userInfo.country,
          city: userInfo.city
        });

        return this.renderLogin('用户名或密码错误');
      }
    }
  },

  // 处理登出
  async handleLogout(request, auth) {
    return new Response('', {
      status: 302,
      headers: {
        'Location': '/',
        'Set-Cookie': auth.createLogoutCookie()
      }
    });
  },

  // 处理添加节点
  async handleAddNode(request, auth, converter, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const type = formData.get('type');
        const name = formData.get('name');
        const config = formData.get('config');

        // 验证输入
        if (!type || !name || !config) {
          throw new Error('请填写完整的节点信息');
        }

        // 解析节点
        const node = converter.parseNode(config.trim(), type);
        node.name = name.trim(); // 使用用户提供的名称
        node.id = Date.now().toString(); // 简单的ID生成

        // 验证解析结果
        if (!node.server || !node.port || !node.uuid) {
          throw new Error('节点配置不完整，请检查链接格式');
        }

        // 获取现有节点
        const existingNodes = await this.getNodes(env);

        // 检查是否已存在相同的节点
        const duplicate = existingNodes.find(existing =>
          existing.server === node.server &&
          existing.port === node.port &&
          existing.uuid === node.uuid
        );

        if (duplicate) {
          throw new Error('该节点已存在');
        }

        existingNodes.push(node);

        // 保存到KV
        await env.NODES_KV.put('nodes', JSON.stringify(existingNodes));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('添加节点失败:', e);
        return this.renderDashboard(request, env, `添加节点失败: ${e.message}`);
      }
    }
  },

  // 处理删除节点
  async handleDeleteNode(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      const { id } = await request.json();
      const nodes = await this.getNodes(env);
      const filteredNodes = nodes.filter(node => node.id !== id);
      await env.NODES_KV.put('nodes', JSON.stringify(filteredNodes));
      return new Response('OK');
    }
  },

  // 处理编辑节点
  async handleEditNode(request, auth, converter, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'GET') {
      // 显示编辑表单
      const url = new URL(request.url);
      const nodeId = url.searchParams.get('id');

      if (!nodeId) {
        return new Response('Missing node ID', { status: 400 });
      }

      const nodes = await this.getNodes(env);
      const node = nodes.find(n => n.id === nodeId);

      if (!node) {
        return new Response('Node not found', { status: 404 });
      }

      return this.renderEditNodeForm(request, env, node);
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const nodeId = formData.get('id');
        const type = formData.get('type');
        const name = formData.get('name');
        const config = formData.get('config');

        // 验证输入
        if (!nodeId || !type || !name || !config) {
          throw new Error('请填写完整的节点信息');
        }

        // 解析节点
        const updatedNode = converter.parseNode(config.trim(), type);
        updatedNode.name = name.trim();
        updatedNode.id = nodeId; // 保持原有ID

        // 验证解析结果
        if (!updatedNode.server || !updatedNode.port || !updatedNode.uuid) {
          throw new Error('节点配置不完整，请检查链接格式');
        }

        // 获取现有节点并更新
        const nodes = await this.getNodes(env);
        const nodeIndex = nodes.findIndex(n => n.id === nodeId);

        if (nodeIndex === -1) {
          throw new Error('节点不存在');
        }

        // 检查是否与其他节点重复
        const duplicate = nodes.find((existing, index) =>
          index !== nodeIndex &&
          existing.server === updatedNode.server &&
          existing.port === updatedNode.port &&
          existing.uuid === updatedNode.uuid
        );

        if (duplicate) {
          throw new Error('该节点配置与其他节点重复');
        }

        nodes[nodeIndex] = updatedNode;

        // 保存到KV
        await env.NODES_KV.put('nodes', JSON.stringify(nodes));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('编辑节点失败:', e);
        const url = new URL(request.url);
        const nodeId = url.searchParams.get('id');
        const nodes = await this.getNodes(env);
        const node = nodes.find(n => n.id === nodeId);
        return this.renderEditNodeForm(request, env, node, `编辑节点失败: ${e.message}`);
      }
    }
  },

  // 处理V2Ray订阅
  async handleV2raySubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const subscription = converter.toV2raySubscription(nodes);

    // 检查是否是浏览器访问（通过User-Agent或Accept头判断）
    const userAgent = request.headers.get('User-Agent') || '';
    const accept = request.headers.get('Accept') || '';
    const isBrowser = userAgent.includes('Mozilla') && accept.includes('text/html');

    if (isBrowser) {
      // 浏览器访问时显示纯文本配置内容
      const decodedContent = atob(subscription); // 解码Base64
      return new Response(decodedContent, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache'
        }
      });
    } else {
      // 客户端访问时返回订阅内容
      return new Response(subscription, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache'
        }
      });
    }
  },

  // 处理Clash订阅
  async handleClashSubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const clashConfig = converter.toClashConfig(nodes);

    // 将JSON转换为YAML格式
    const yamlContent = this.jsonToYaml(clashConfig);

    // 直接返回YAML内容，无论是浏览器还是客户端访问
    return new Response(yamlContent, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    });
  },

  // 处理Surge订阅
  async handleSurgeSubscription(converter, env, request) {
    // 检查邮箱验证
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response('需要提供email参数', {
        status: 400,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    // 验证邮箱是否在可信列表中
    const isValidEmail = await this.validateEmail(email, env);
    if (!isValidEmail) {
      return new Response('邮箱未授权访问', {
        status: 403,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
      });
    }

    const nodes = await this.getNodes(env);
    const surgeConfig = this.generateSurgeConfig(nodes);

    // 直接返回配置内容，无论是浏览器还是客户端访问
    return new Response(surgeConfig, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    });
  },

  // 获取节点列表
  async getNodes(env) {
    try {
      const nodesData = await env.NODES_KV.get('nodes');
      return nodesData ? JSON.parse(nodesData) : [];
    } catch (e) {
      return [];
    }
  },

  // 获取可信邮箱列表
  async getTrustedEmails(env) {
    try {
      const emailsData = await env.NODES_KV.get('trusted_emails');
      return emailsData ? JSON.parse(emailsData) : [];
    } catch (e) {
      return [];
    }
  },

  // 验证邮箱是否可信
  async validateEmail(email, env) {
    const trustedEmails = await this.getTrustedEmails(env);
    return trustedEmails.includes(email.toLowerCase());
  },

  // 处理添加邮箱
  async handleAddEmail(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      try {
        const formData = await request.formData();
        const email = formData.get('email');

        // 验证邮箱格式
        if (!email || !this.isValidEmailFormat(email)) {
          throw new Error('请输入有效的邮箱地址');
        }

        // 获取现有邮箱列表
        const existingEmails = await this.getTrustedEmails(env);
        const normalizedEmail = email.toLowerCase().trim();

        // 检查是否已存在
        if (existingEmails.includes(normalizedEmail)) {
          throw new Error('该邮箱已存在');
        }

        existingEmails.push(normalizedEmail);

        // 保存到KV
        await env.NODES_KV.put('trusted_emails', JSON.stringify(existingEmails));

        return new Response('', {
          status: 302,
          headers: { 'Location': '/' }
        });
      } catch (e) {
        console.error('添加邮箱失败:', e);
        return this.renderDashboard(request, env, `添加邮箱失败: ${e.message}`);
      }
    }
  },

  // 处理删除邮箱
  async handleDeleteEmail(request, auth, env) {
    if (!auth.isAuthenticated(request)) {
      return new Response('Unauthorized', { status: 401 });
    }

    if (request.method === 'POST') {
      const { email } = await request.json();
      const emails = await this.getTrustedEmails(env);
      const filteredEmails = emails.filter(e => e !== email);
      await env.NODES_KV.put('trusted_emails', JSON.stringify(filteredEmails));
      return new Response('OK');
    }
  },

  // 验证邮箱格式
  isValidEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 获取用户基本信息
  getUserInfo(request) {
    const userAgent = request.headers.get('User-Agent') || '';
    const cfConnectingIp = request.headers.get('CF-Connecting-IP');
    const xForwardedFor = request.headers.get('X-Forwarded-For');
    const xRealIp = request.headers.get('X-Real-IP');

    // 获取真实IP地址
    const ip = cfConnectingIp ||
               (xForwardedFor && xForwardedFor.split(',')[0].trim()) ||
               xRealIp ||
               '未知';

    // 解析浏览器信息
    const browser = this.parseBrowser(userAgent);
    const platform = this.parsePlatform(userAgent);

    // 获取地理位置信息（Cloudflare提供）
    const country = request.cf?.country || '未知';
    const city = request.cf?.city || '未知';

    return {
      ip,
      userAgent,
      browser,
      platform,
      country,
      city
    };
  },

  // 解析浏览器信息
  parseBrowser(userAgent) {
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      return 'Safari';
    } else if (userAgent.includes('Edg')) {
      return 'Edge';
    } else if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
      return 'Opera';
    } else {
      return '未知浏览器';
    }
  },

  // 解析平台信息
  parsePlatform(userAgent) {
    if (userAgent.includes('Windows')) {
      return 'Windows';
    } else if (userAgent.includes('Mac OS X') || userAgent.includes('Macintosh')) {
      return 'macOS';
    } else if (userAgent.includes('Linux')) {
      return 'Linux';
    } else if (userAgent.includes('Android')) {
      return 'Android';
    } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'iOS';
    } else {
      return '未知平台';
    }
  },

  // 渲染登录页面
  renderLogin(errorMessage = '') {
    const css = this.getCSS();
    let html = this.getLoginHTML();
    html = html.replace('{{CSS}}', css);
    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ? 
      `<div class="alert alert-error">${errorMessage}</div>` : '');
    
    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 渲染管理面板
  async renderDashboard(request, env, errorMessage = '') {
    const css = this.getCSS();
    const nodes = await this.getNodes(env);
    const emails = await this.getTrustedEmails(env);
    const baseUrl = new URL(request.url).origin;

    let html = this.getDashboardHTML();
    html = html.replace('{{CSS}}', css);
    // 替换所有的 {{BASE_URL}} 占位符
    html = html.replace(/\{\{BASE_URL\}\}/g, baseUrl);
    html = html.replace('{{NODES_LIST}}', this.generateNodesList(nodes));
    html = html.replace('{{EMAILS_LIST}}', this.generateEmailsList(emails));
    html = html.replace('{{ERROR_MESSAGE}}', errorMessage ?
      `<div class="alert alert-error">${errorMessage}</div>` : '');

    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 生成现代化节点列表HTML
  generateNodesList(nodes) {
    if (nodes.length === 0) {
      return `<div class="empty-state">
        <div style="width: 64px; height: 64px; background: var(--gray-100); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-4) auto; color: var(--gray-400);">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="9" cy="12" r="1"></circle>
            <circle cx="9" cy="5" r="1"></circle>
            <circle cx="9" cy="19" r="1"></circle>
            <path d="M20 12h.01"></path>
            <path d="M20 5h.01"></path>
            <path d="M20 19h.01"></path>
            <path d="M3 12h.01"></path>
            <path d="M3 5h.01"></path>
            <path d="M3 19h.01"></path>
          </svg>
        </div>
        <h3 style="color: var(--gray-500); margin-bottom: var(--space-2); font-weight: 500;">暂无节点</h3>
        <p style="color: var(--gray-400); font-size: var(--text-sm); margin: 0;">请在上方表单中添加您的第一个节点</p>
      </div>`;
    }

    return nodes.map(node => `
      <div class="node-item">
        <div class="node-info">
          <div class="node-name">${node.name}</div>
          <div class="node-details">
            <span class="node-type">${node.type.toUpperCase()}</span>
            <span class="node-server">${node.server}:${node.port}</span>
            ${node.network ? `<span style="color: var(--gray-400); font-size: var(--text-xs); background: var(--gray-100); padding: var(--space-1) var(--space-2); border-radius: var(--radius-sm);">${node.network}</span>` : ''}
          </div>
        </div>
        <div class="node-actions">
          <button class="btn-edit" onclick="editNode('${node.id}')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            编辑
          </button>
          <button class="btn-danger" onclick="deleteNode('${node.id}')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
            删除
          </button>
        </div>
      </div>
    `).join('');
  },

  // 生成现代化邮箱列表HTML
  generateEmailsList(emails) {
    if (emails.length === 0) {
      return `<div class="empty-state">
        <div style="width: 64px; height: 64px; background: var(--gray-100); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-4) auto; color: var(--gray-400);">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
        </div>
        <h3 style="color: var(--gray-500); margin-bottom: var(--space-2); font-weight: 500;">暂无可信邮箱</h3>
        <p style="color: var(--gray-400); font-size: var(--text-sm); margin: 0;">请添加可以访问订阅的邮箱地址</p>
      </div>`;
    }

    return emails.map(email => `
      <div class="email-item">
        <div class="email-info">
          <div class="email-address">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: inline; vertical-align: text-bottom; margin-right: var(--space-2); color: var(--primary-500);">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            ${email}
          </div>
        </div>
        <div class="email-actions">
          <button class="btn-danger" onclick="deleteEmail('${email}')">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
            删除
          </button>
        </div>
      </div>
    `).join('');
  },

  // 渲染编辑节点表单
  async renderEditNodeForm(request, env, node, errorMessage = '') {
    const css = this.getCSS();
    const baseUrl = new URL(request.url).origin;

    // 重新生成节点配置链接
    let nodeConfig = '';
    const { NodeConverter } = require('./converter.js');
    const converter = new NodeConverter();

    if (node.type === 'vmess') {
      nodeConfig = converter.nodeToVmessLink(node);
    } else if (node.type === 'vless') {
      nodeConfig = converter.nodeToVlessLink(node);
    }

    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑节点 - 订阅转换器</title>
    <style>${css}</style>
</head>
<body>
    <div class="dashboard-container">
        <div class="container">
            <div class="header">
                <h1>✏️ 编辑节点</h1>
                <p>修改节点配置信息</p>
            </div>

            ${errorMessage ? `<div class="alert alert-error">${errorMessage}</div>` : ''}

            <div class="edit-page-grid">
                <div class="container">
                    <h2>📝 节点信息</h2>
                    <form method="POST" action="/edit-node">
                        <input type="hidden" name="id" value="${node.id}">
                        <div class="form-group">
                            <label for="node-type">🔧 节点类型:</label>
                            <select id="node-type" name="type" required>
                                <option value="vmess" ${node.type === 'vmess' ? 'selected' : ''}>VMess</option>
                                <option value="vless" ${node.type === 'vless' ? 'selected' : ''}>VLess</option>
                                <option value="ss" ${node.type === 'ss' ? 'selected' : ''}>Shadowsocks</option>
                                <option value="trojan" ${node.type === 'trojan' ? 'selected' : ''}>Trojan</option>
                                <option value="http" ${node.type === 'http' ? 'selected' : ''}>HTTP/HTTPS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="node-name">📝 节点名称:</label>
                            <input type="text" id="node-name" name="name" value="${node.name}" required placeholder="例如: 香港节点01">
                        </div>
                        <div class="form-group">
                            <label for="node-config">⚙️ 节点配置:</label>
                            <textarea id="node-config" name="config" rows="6" required placeholder="粘贴节点链接或配置信息">${nodeConfig}</textarea>
                            <small class="form-help">支持 VLess、VMess、Shadowsocks、Trojan 等格式的链接</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary">💾 保存修改</button>
                            <a href="/" class="btn-secondary">❌ 取消</a>
                        </div>
                    </form>
                </div>

                <!-- 当前配置预览 -->
                <div class="container">
                    <h2>🔍 当前配置</h2>
                    <div class="config-preview">
                        <div class="config-item">
                            <strong>服务器:</strong> ${node.server}:${node.port}
                        </div>
                        <div class="config-item">
                            <strong>UUID:</strong> ${node.uuid}
                        </div>
                        ${node.network ? `<div class="config-item"><strong>网络:</strong> ${node.network}</div>` : ''}
                        ${node.security ? `<div class="config-item"><strong>安全:</strong> ${node.security}</div>` : ''}
                        ${node.sni ? `<div class="config-item"><strong>SNI:</strong> ${node.sni}</div>` : ''}
                        ${node.flow ? `<div class="config-item"><strong>Flow:</strong> ${node.flow}</div>` : ''}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = \`notification notification-\${type}\`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
    </script>
</body>
</html>`;

    return new Response(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  },

  // 生成Surge配置
  generateSurgeConfig(nodes) {
    const proxies = nodes.map(node => {
      switch (node.type) {
        case 'ss':
          return `${node.name} = ss, ${node.server}, ${node.port}, encrypt-method=${node.method}, password=${node.password}`;
        case 'http':
          return `${node.name} = http, ${node.server}, ${node.port}${node.username ? `, username=${node.username}, password=${node.password}` : ''}`;
        default:
          return null;
      }
    }).filter(Boolean);

    return `[General]
skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, localhost, *.local

[Proxy]
${proxies.join('\n')}

[Proxy Group]
Proxy = select, ${nodes.map(n => n.name).join(', ')}

[Rule]
FINAL,Proxy`;
  },

  // 简单的JSON到YAML转换器
  jsonToYaml(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === 'object' && item !== null) {
          yaml += `${spaces}- ${this.jsonToYaml(item, indent + 1).trim()}\n`;
        } else {
          yaml += `${spaces}- ${this.escapeYamlValue(item)}\n`;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else if (typeof value === 'object' && value !== null) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else {
          yaml += `${spaces}${key}: ${this.escapeYamlValue(value)}\n`;
        }
      }
    }

    return yaml;
  },

  // 转义YAML值
  escapeYamlValue(value) {
    if (typeof value === 'string') {
      // 如果字符串包含特殊字符，需要加引号
      if (value.includes(':') || value.includes('#') || value.includes('[') ||
          value.includes(']') || value.includes('{') || value.includes('}') ||
          value.includes('|') || value.includes('>') || value.includes('&') ||
          value.includes('*') || value.includes('!') || value.includes('%') ||
          value.includes('@') || value.includes('`')) {
        return `"${value.replace(/"/g, '\\"')}"`;
      }
      return value;
    }
    return value;
  },

  // 生成配置显示页面
  generateConfigDisplayPage(type, content, nodes, rawContent) {
    const nodeCount = nodes.length;

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${type} 配置 - 订阅转换器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            padding: 2rem;
            background: #f8f9fa;
        }
        .stat-item {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        .content-section {
            padding: 2rem;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .content-title {
            font-size: 1.5rem;
            color: #333;
        }
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .copy-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .config-content {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 10px;
            padding: 1.5rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .usage-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
        }
        .usage-info h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        .usage-info ul {
            list-style: none;
            padding-left: 0;
        }
        .usage-info li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }
        .usage-info li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        .footer {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        .success-message.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 ${type} 配置文件</h1>
            <p>订阅转换器生成的配置内容</p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">${nodeCount}</div>
                <div class="stat-label">节点数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${Math.round(content.length / 1024)}KB</div>
                <div class="stat-label">配置大小</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${content.split('\\n').length}</div>
                <div class="stat-label">配置行数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${type}</div>
                <div class="stat-label">配置类型</div>
            </div>
        </div>

        <div class="usage-info">
            <h3>📖 使用说明</h3>
            <ul>
                <li><strong>客户端导入</strong>: 复制当前页面URL到${type}客户端的订阅链接中</li>
                <li><strong>手动配置</strong>: 点击"复制配置"按钮，将内容粘贴到客户端配置文件中</li>
                <li><strong>自动更新</strong>: 客户端会定期从此链接更新配置</li>
                <li><strong>浏览器访问</strong>: 显示此页面；客户端访问: 直接返回配置内容</li>
            </ul>
        </div>

        <div class="content-section">
            <div class="content-header">
                <h2 class="content-title">📄 配置内容</h2>
                <button class="copy-btn" onclick="copyConfig()">📋 复制配置</button>
            </div>
            <div class="config-content" id="config-content">${content}</div>
        </div>

        <div class="footer">
            <a href="/">← 返回管理面板</a>
        </div>
    </div>

    <div class="success-message" id="success-message">
        ✅ 配置已复制到剪贴板！
    </div>

    <script>
        function copyConfig() {
            const content = \`${rawContent.replace(/`/g, '\\`').replace(/\$/g, '\\$').replace(/\\\\/g, '\\\\\\\\')}\`;
            navigator.clipboard.writeText(content).then(function() {
                showSuccessMessage();
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = '✅ 已复制!';
                btn.style.background = '#28a745';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#28a745';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择内容复制');
            });
        }

        function showSuccessMessage() {
            const message = document.getElementById('success-message');
            message.classList.add('show');
            setTimeout(() => {
                message.classList.remove('show');
            }, 3000);
        }

        // 语法高亮（简单版）
        function highlightSyntax() {
            const content = document.getElementById('config-content');
            let html = content.textContent;

            // 简单的YAML语法高亮
            if ('${type}' === 'Clash') {
                html = html.replace(/^([a-zA-Z-]+):/gm, '<span style="color: #569cd6;">$1</span>:');
                html = html.replace(/: ([^\\n]+)/g, ': <span style="color: #ce9178;">$1</span>');
                html = html.replace(/#[^\\n]*/g, '<span style="color: #6a9955;">$&</span>');
            }

            content.innerHTML = html;
        }

        // 页面加载完成后执行语法高亮
        document.addEventListener('DOMContentLoaded', highlightSyntax);
    </script>
</body>
</html>`;
  },

  // 获取现代化CSS内容
  getCSS() {
    return `/* Modern CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color System */
  --primary-50: #f0f4ff;
  --primary-100: #e0edff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  color: var(--gray-800);
  line-height: 1.6;
  font-size: var(--text-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Login Page Styles */
.login-body {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--space-4);
}

/* Container System */
.container {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-8);
  width: 100%;
  margin-bottom: var(--space-6);
  border: 1px solid var(--gray-100);
  backdrop-filter: blur(10px);
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Grid Layouts */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.edit-page-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-top: var(--space-8);
}

/* Typography */
h1, h2 {
  font-weight: 700;
  text-align: center;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--text-3xl);
  background: linear-gradient(135deg, #1e293b, #0f172a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: var(--text-2xl);
  color: var(--gray-800);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-5);
}

label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--gray-700);
  font-weight: 500;
  font-size: var(--text-sm);
}

input, textarea, select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  color: var(--gray-900);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  transform: translateY(-1px);
}

input::placeholder, textarea::placeholder {
  color: var(--gray-400);
}

textarea {
  min-height: 120px;
  resize: vertical;
}

select {
  cursor: pointer;
}

/* Button System */
button, .btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

button {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Variants */
.btn-secondary {
  background: var(--gray-600);
  color: white;
  width: auto;
  margin-top: var(--space-4);
}

.btn-secondary:hover {
  background: var(--gray-700);
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
  color: white;
  width: auto;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.btn-danger:hover {
  background: linear-gradient(135deg, var(--error-600), var(--error-500));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-edit {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
  width: auto;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  margin-right: var(--space-2);
}

.btn-edit:hover {
  background: linear-gradient(135deg, var(--success-600), var(--success-500));
  transform: translateY(-1px);
}

.copy-btn {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
  width: auto;
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.copy-btn:hover {
  background: linear-gradient(135deg, var(--success-600), var(--success-500));
  transform: translateY(-1px);
}

/* Node Components */
.node-item {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.node-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-200);
}

.node-info {
  flex: 1;
  min-width: 0;
}

.node-name {
  font-weight: 600;
  font-size: var(--text-lg);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
  line-height: 1.4;
}

.node-details {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  font-size: var(--text-sm);
  color: var(--gray-600);
  flex-wrap: wrap;
}

.node-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  margin-left: var(--space-4);
}

.node-type {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  color: var(--primary-700);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.node-server {
  color: var(--gray-500);
  font-size: var(--text-sm);
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Email Components */
.email-item {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.email-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  border-color: var(--primary-200);
}

.email-info {
  flex: 1;
  min-width: 0;
}

.email-address {
  font-weight: 500;
  font-size: var(--text-base);
  color: var(--gray-900);
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

.email-actions {
  margin-left: var(--space-4);
}

/* Subscription Links */
.subscription-links {
  background: linear-gradient(135deg, var(--gray-50), white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  margin-top: var(--space-8);
}

.link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-4) var(--space-6);
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.link-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-200);
}

.link-item span {
  font-weight: 500;
  color: var(--gray-800);
  font-size: var(--text-base);
}

/* Alerts */
.alert {
  padding: var(--space-4) var(--space-6);
  margin-bottom: var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  border-left: 4px solid;
  font-size: var(--text-sm);
}

.alert-success {
  background: var(--success-50);
  color: var(--success-600);
  border-left-color: var(--success-500);
  border: 1px solid var(--success-200);
}

.alert-error {
  background: var(--error-50);
  color: var(--error-600);
  border-left-color: var(--error-500);
  border: 1px solid var(--error-200);
}

/* Empty States */
.empty-state {
  text-align: center;
  color: var(--gray-500);
  font-style: italic;
  padding: var(--space-12);
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--gray-300);
  margin: var(--space-6) 0;
}

/* Config Preview */
.config-preview {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.config-item {
  margin-bottom: var(--space-3);
  padding: var(--space-3);
  background: white;
  border-radius: var(--radius-md);
  border-left: 3px solid var(--primary-500);
  box-shadow: var(--shadow-sm);
}

.config-item:last-child {
  margin-bottom: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.form-actions .btn-secondary {
  flex: 1;
  margin-top: 0;
  text-align: center;
}

.form-help {
  color: var(--gray-500);
  font-size: var(--text-xs);
  margin-top: var(--space-1);
  display: block;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.header h1 {
  margin-bottom: var(--space-2);
}

.header p {
  color: var(--gray-600);
  margin: 0;
  font-size: var(--text-lg);
}

/* Responsive Design */
@media (max-width: 640px) {
  .container {
    padding: var(--space-4);
    margin: var(--space-2);
    border-radius: var(--radius-xl);
  }

  .dashboard-container {
    padding: 0 var(--space-2);
  }

  .dashboard-grid, .edit-page-grid {
    gap: var(--space-4);
    margin-top: var(--space-4);
  }

  h1 {
    font-size: var(--text-2xl);
  }

  h2 {
    font-size: var(--text-xl);
  }

  .node-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
    padding: var(--space-4);
  }

  .node-actions {
    margin-left: 0;
    justify-content: center;
  }

  .node-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .link-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
    text-align: center;
  }

  .copy-btn {
    width: 100%;
  }

  .email-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
    text-align: center;
  }

  .email-actions {
    margin-left: 0;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .edit-page-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .dashboard-container {
    padding: 0 var(--space-8);
  }

  .container {
    padding: var(--space-10);
  }

  .dashboard-grid {
    gap: var(--space-8);
    max-width: 1200px;
    margin: var(--space-8) auto 0 auto;
  }

  .edit-page-grid {
    max-width: 1000px;
    margin: var(--space-8) auto 0 auto;
  }

  h1 {
    font-size: var(--text-4xl);
  }

  h2 {
    font-size: var(--text-3xl);
  }

  .subscription-links {
    padding: var(--space-10);
  }

  .node-item {
    padding: var(--space-8);
  }
}

/* Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.container, .node-item, .email-item, .link-item {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus-visible, input:focus-visible, textarea:focus-visible, select:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast support */
@media (prefers-contrast: high) {
  :root {
    --gray-200: #000;
    --gray-300: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}`;
  },

  // 获取现代化登录页面HTML
  getLoginHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换器 - 登录</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>{{CSS}}</style>
</head>
<body class="login-body">
    <div class="container" style="max-width: 420px; margin: 0 auto;">
        <div class="header">
            <div style="width: 64px; height: 64px; background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-4) auto; font-size: 2rem;">🚀</div>
            <h1 style="margin-bottom: var(--space-2);">欢迎回来</h1>
            <p style="color: var(--gray-600); margin: 0 0 var(--space-8) 0; font-size: var(--text-base);">登录到您的订阅管理平台</p>
        </div>
        
        {{ERROR_MESSAGE}}
        
        <form method="POST" action="/login" style="space-y: var(--space-6);">
            <div class="form-group">
                <label for="username">
                    <span style="display: flex; align-items: center; gap: var(--space-2);">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        用户名
                    </span>
                </label>
                <input type="text" id="username" name="username" required autocomplete="username" placeholder="请输入用户名">
            </div>
            
            <div class="form-group">
                <label for="password">
                    <span style="display: flex; align-items: center; gap: var(--space-2);">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <circle cx="12" cy="16" r="1"></circle>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                        密码
                    </span>
                </label>
                <input type="password" id="password" name="password" required autocomplete="current-password" placeholder="请输入密码">
            </div>
            
            <button type="submit" style="margin-top: var(--space-6);">
                <span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                        <polyline points="10,17 15,12 10,7"></polyline>
                        <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    登录
                </span>
            </button>
        </form>
        
        <div style="margin-top: var(--space-6); padding-top: var(--space-6); border-top: 1px solid var(--gray-200); text-align: center;">
            <p style="color: var(--gray-500); font-size: var(--text-sm);">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: inline; vertical-align: text-bottom; margin-right: var(--space-1);">
                    <path d="M9 12l2 2 4-4"></path>
                    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                    <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                </svg>
                安全加密传输
            </p>
        </div>
    </div>
    
    <script>
        // 添加登录表单增强
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const button = form.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            form.addEventListener('submit', function() {
                button.disabled = true;
                button.innerHTML = '<span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="animation: spin 1s linear infinite;"><line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line></svg>登录中...</span>';
                
                // 5秒后恢复按钮状态
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = originalText;
                }, 5000);
            });
        });
    </script>
    
    <style>
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</body>
</html>`;
  },

  // 获取现代化管理面板HTML
  getDashboardHTML() {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换器 - 管理面板</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>{{CSS}}</style>
</head>
<body>
    <div class="dashboard-container">
        <header style="text-align: center; margin-bottom: var(--space-8); padding-top: var(--space-8);">
            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); border-radius: var(--radius-2xl); display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-4) auto; font-size: 2.5rem; box-shadow: var(--shadow-lg);">🚀</div>
            <h1 style="background: linear-gradient(135deg, #1e293b, #0f172a); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">节点管理面板</h1>
            <p style="color: var(--gray-600); font-size: var(--text-lg); margin: 0;">管理您的代理节点和订阅链接</p>
        </header>

        {{ERROR_MESSAGE}}

        <div class="main-content-wrapper">
            <div class="dashboard-grid">
                <!-- 添加节点表单 -->
                <div class="container">
                    <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-6);">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--success-500), var(--success-600)); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </div>
                        <div>
                            <h2 style="margin: 0; font-size: var(--text-xl); text-align: left;">添加新节点</h2>
                            <p style="margin: 0; color: var(--gray-600); font-size: var(--text-sm);">支持 VMess、VLess、Shadowsocks 等协议</p>
                        </div>
                    </div>
                    
                    <form method="POST" action="/add-node">
                        <div class="form-group">
                            <label for="node-type">节点类型</label>
                            <select id="node-type" name="type" required>
                                <option value="">选择节点类型</option>
                                <option value="vmess">VMess</option>
                                <option value="vless">VLess</option>
                                <option value="ss">Shadowsocks</option>
                                <option value="trojan">Trojan</option>
                                <option value="http">HTTP/HTTPS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="node-name">节点名称</label>
                            <input type="text" id="node-name" name="name" required placeholder="例如: 香港节点01">
                        </div>
                        <div class="form-group">
                            <label for="node-config">节点配置</label>
                            <textarea id="node-config" name="config" rows="4" required placeholder="粘贴节点链接或配置信息"></textarea>
                            <small class="form-help">支持 VLess、VMess、Shadowsocks、Trojan 等格式的链接</small>
                        </div>
                        <button type="submit">
                            <span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                添加节点
                            </span>
                        </button>
                    </form>
                </div>

                <!-- 订阅链接 -->
                <div class="container subscription-links">
                    <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-6);">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--primary-500), var(--primary-600)); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 style="margin: 0; font-size: var(--text-xl); text-align: left;">订阅链接</h2>
                            <p style="margin: 0; color: var(--gray-600); font-size: var(--text-sm);">复制链接到客户端使用</p>
                        </div>
                    </div>
                    
                    <div class="link-item">
                        <span style="display: flex; align-items: center; gap: var(--space-2);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                                <path d="M2 17l10 5 10-5"></path>
                                <path d="M2 12l10 5 10-5"></path>
                            </svg>
                            V2Ray订阅
                        </span>
                        <button class="copy-btn" onclick="copySubscriptionLink('v2ray')">复制链接</button>
                    </div>
                    <div class="link-item">
                        <span style="display: flex; align-items: center; gap: var(--space-2);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"></path>
                            </svg>
                            Clash订阅
                        </span>
                        <button class="copy-btn" onclick="copySubscriptionLink('clash')">复制链接</button>
                    </div>
                    <div class="link-item">
                        <span style="display: flex; align-items: center; gap: var(--space-2);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                                <line x1="12" y1="22.08" x2="12" y2="12"></line>
                            </svg>
                            Surge订阅
                        </span>
                        <button class="copy-btn" onclick="copySubscriptionLink('surge')">复制链接</button>
                    </div>
                    <div style="margin-top: var(--space-4); padding: var(--space-4); background: var(--warning-50); border: 1px solid var(--warning-200); border-radius: var(--radius-lg);">
                        <div style="display: flex; align-items: start; gap: var(--space-2);">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="var(--warning-500)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-top: 2px; flex-shrink: 0;">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <div>
                                <p style="color: var(--warning-600); font-size: var(--text-sm); margin: 0; font-weight: 500;">订阅链接说明</p>
                                <p style="color: var(--warning-600); font-size: var(--text-xs); margin: var(--space-1) 0 0 0;">所有订阅都需要邮箱验证，格式为 /订阅类型?email=<EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邮箱管理 -->
                <div class="container">
                    <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-6);">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--primary-400), var(--primary-500)); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </div>
                        <div>
                            <h2 style="margin: 0; font-size: var(--text-xl); text-align: left;">可信邮箱管理</h2>
                            <p style="margin: 0; color: var(--gray-600); font-size: var(--text-sm);">添加可以访问订阅的邮箱地址</p>
                        </div>
                    </div>
                    
                    <form method="POST" action="/add-email">
                        <div class="form-group">
                            <label for="email">邮箱地址</label>
                            <input type="email" id="email" name="email" required placeholder="例如: <EMAIL>">
                        </div>
                        <button type="submit">
                            <span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                添加邮箱
                            </span>
                        </button>
                    </form>
                    
                    <div style="margin-top: var(--space-4); padding: var(--space-4); background: var(--error-50); border: 1px solid var(--error-200); border-radius: var(--radius-lg);">
                        <div style="display: flex; align-items: start; gap: var(--space-2);">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="var(--error-500)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-top: 2px; flex-shrink: 0;">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                <line x1="12" y1="9" x2="12" y2="13"></line>
                                <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            <div>
                                <p style="color: var(--error-600); font-size: var(--text-sm); margin: 0; font-weight: 500;">安全提醒</p>
                                <p style="color: var(--error-600); font-size: var(--text-xs); margin: var(--space-1) 0 0 0;">只有添加到可信列表的邮箱才能访问所有订阅（V2Ray、Clash、Surge）</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邮箱列表 -->
                <div class="container">
                    <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-6);">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--gray-400), var(--gray-500)); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                            </svg>
                        </div>
                        <div>
                            <h2 style="margin: 0; font-size: var(--text-xl); text-align: left;">已添加的邮箱</h2>
                            <p style="margin: 0; color: var(--gray-600); font-size: var(--text-sm);">当前可以访问订阅的邮箱列表</p>
                        </div>
                    </div>
                    {{EMAILS_LIST}}
                </div>
            </div>

            <!-- 节点列表 -->
            <div class="nodes-container">
                <div class="container">
                    <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-6);">
                        <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--gray-600), var(--gray-700)); border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="9" cy="12" r="1"></circle>
                                <circle cx="9" cy="5" r="1"></circle>
                                <circle cx="9" cy="19" r="1"></circle>
                                <path d="M20 12h.01"></path>
                                <path d="M20 5h.01"></path>
                                <path d="M20 19h.01"></path>
                                <path d="M3 12h.01"></path>
                                <path d="M3 5h.01"></path>
                                <path d="M3 19h.01"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 style="margin: 0; font-size: var(--text-xl); text-align: left;">已添加的节点</h2>
                            <p style="margin: 0; color: var(--gray-600); font-size: var(--text-sm);">当前配置的所有代理节点</p>
                        </div>
                    </div>
                    {{NODES_LIST}}
                </div>
            </div>

            <!-- 退出登录 -->
            <div class="logout-container">
                <div class="container" style="text-align: center;">
                    <form method="POST" action="/logout">
                        <button type="submit" class="btn-secondary" style="width: auto; margin: 0; padding: var(--space-3) var(--space-8);">
                            <span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                    <polyline points="16,17 21,12 16,7"></polyline>
                                    <line x1="21" y1="12" x2="9" y2="12"></line>
                                </svg>
                                退出登录
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('✅ 链接已复制到剪贴板', 'success');
            }).catch(function(err) {
                console.error('复制失败:', err);
                showNotification('❌ 复制失败，请手动复制', 'error');
                // 降级方案：显示链接让用户手动复制
                prompt('请手动复制以下链接:', text);
            });
        }

        function copySubscriptionLink(type) {
            const email = prompt('请输入你的邮箱地址:', '');
            if (!email) {
                showNotification('❌ 需要提供邮箱地址', 'error');
                return;
            }

            // 简单的邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showNotification('❌ 邮箱格式不正确', 'error');
                return;
            }

            const baseUrl = window.location.origin;
            const subscriptionUrl = \`\${baseUrl}/\${type}?email=\${encodeURIComponent(email)}\`;

            copyToClipboard(subscriptionUrl);
        }

        function deleteNode(nodeId) {
            if (confirm('确定要删除这个节点吗？')) {
                const button = event.target;
                button.disabled = true;
                button.textContent = '删除中...';

                fetch('/delete-node', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: nodeId })
                }).then(response => {
                    if (response.ok) {
                        showNotification('✅ 节点删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error('删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    showNotification('❌ 删除失败，请重试', 'error');
                    button.disabled = false;
                    button.textContent = '🗑️ 删除';
                });
            }
        }

        function editNode(nodeId) {
            window.location.href = '/edit-node?id=' + nodeId;
        }

        function deleteEmail(email) {
            if (confirm('确定要删除这个邮箱吗？')) {
                const button = event.target;
                button.disabled = true;
                button.textContent = '删除中...';

                fetch('/delete-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                }).then(response => {
                    if (response.ok) {
                        showNotification('✅ 邮箱删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error('删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    showNotification('❌ 删除失败，请重试', 'error');
                    button.disabled = false;
                    button.textContent = '🗑️ 删除';
                });
            }
        }

        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = \`alert alert-\${type === 'success' ? 'success' : 'error'}\`;
            notification.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
                max-width: 400px;
                box-shadow: var(--shadow-xl);
            \`;
            notification.textContent = message;

            // 添加动画样式
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = \`
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                \`;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 表单提交增强
        document.addEventListener('DOMContentLoaded', function() {
            const nodeForm = document.querySelector('form[action="/add-node"]');
            if (nodeForm) {
                nodeForm.addEventListener('submit', function(e) {
                    const button = nodeForm.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;
                    button.disabled = true;
                    button.innerHTML = '<span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="animation: spin 1s linear infinite;"><line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line></svg>添加中...</span>';

                    // 如果表单提交失败，恢复按钮状态
                    setTimeout(() => {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }, 5000);
                });
            }

            const emailForm = document.querySelector('form[action="/add-email"]');
            if (emailForm) {
                emailForm.addEventListener('submit', function(e) {
                    const button = emailForm.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;
                    button.disabled = true;
                    button.innerHTML = '<span style="display: flex; align-items: center; justify-content: center; gap: var(--space-2);"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="animation: spin 1s linear infinite;"><line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line></svg>添加中...</span>';

                    // 如果表单提交失败，恢复按钮状态
                    setTimeout(() => {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }, 5000);
                });
            }
        });
    </script>
    
    <style>
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</body>
</html>`;
  }
};
